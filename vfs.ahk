
#SingleInstance Force


p = %1% ; Z4Q02Oeb70ib
mnt = %2%
if mnt is space
{
	IniRead, mnt, vfs.ini, Config, MountPath, c:\apps
}

IfNotExist, %A_ScriptDir%\fs
	FileCreateDir, %A_ScriptDir%\fs
	
if A_IsCompiled
{
	FileInstall, securefs.exe, %A_Temp%\securefs.exe, 1
	FileInstall, .config.pb, fs\.config.pb, 1
}	
Else
{
	FileCopy, setup\winfsp.msi, %A_ScriptDir%\winfsp.msi, 1
	FileCopy, securefs.exe, %A_Temp%\securefs.exe, 1
	FileCopy, .config.pb, fs\.config.pb, 1
}	


if InStr(FileExist("C:\Program Files (x86)\WinFsp"), "D")
{
	Gosub VFS
}
Else
{
	FileAppend, %A_Now%: VFS winfsp not found`n, vfs.log
	FileInstall, setup\winfsp.msi, %A_ScriptDir%\winfsp.msi, 1
	if FileExist("winfsp.msi")
	{	
		FileAppend, %A_Now%: VFS installing winfsp`n, vfs.log
		RunWait, msiexec /i "%A_ScriptDir%\winfsp.msi" /norestart
		If Not Errorlevel
		{
			FileAppend, %A_Now%: VFS winfsp installed`n, vfs.log
			FileDelete, winfsp.msi
			Gosub VFS
		}	
		Else
		{	
			FileAppend, %A_Now%: VFS winfsp installation error`n, vfs.log
			FileDelete, winfsp.msi
			Exitapp, 100
		}
	}	
	Else
	{
		FileAppend, %A_Now%: VFS install winfsp from https://github.com/winfsp/winfsp/releases`n, vfs.log
		exitapp, 100
	}		
}

ExitApp %RetError%


VFS:
if p is space
{
	; Read mount path from config file for unmount operation
	IniRead, saved_mnt, vfs.ini, Config, MountPath, c:\apps

	RunWait, %A_Temp%\securefs.exe ismount "%saved_mnt%", , Hide UseErrorLevel
	if not errorlevel
	{
		FileAppend, %A_Now%: VFS filesystem is mounted at %saved_mnt%`n, vfs.log
		RunWait, %A_Temp%\securefs.exe u "%saved_mnt%", , Hide UseErrorLevel
		RetError = %ErrorLevel%
		If RetError
		{
			FileAppend, %A_Now%: VFS dismount error %RetError%`n, vfs.log
		}
		Else
		{
			FileAppend, %A_Now%: VFS filesystem dismounted from %saved_mnt%`n, vfs.log
		}
	}
	Else
	{
		FileAppend, %A_Now%: VFS no password`n, vfs.log
	}

	Return
}


if FileExist(A_Temp . "\securefs.exe")
{
	if FileExist("fs\.config.pb")
	{
		RunWait, %A_Temp%\securefs.exe m --log fs.log -b --pass %p% "%A_ScriptDir%\fs" "%mnt%", , Hide UseErrorLevel
		RetError = %ErrorLevel%
		If RetError
		{
			FileAppend, %A_Now%: VFS Error %RetError%`n, vfs.log
		}
		Else
		{
			FileAppend, %A_Now%: VFS filesystem mounted at %mnt%`n, vfs.log
			; Save mount path to config file for future unmount operations
			IniWrite, %mnt%, vfs.ini, Config, MountPath
		}
	}
	Else
	{
		FileAppend, %A_Now%: fs\.config.pb not found`n, vfs.log
		Exitapp, 100
	}
}
Else
{
	FileAppend, %A_Now%: VFS securefs.exe not found`n, vfs.log
	ExitApp, 100
}
	
Return	
