[Info] [0000000000001AFC] [2025-08-01 16:13:43.611804100 UTC] [void __cdecl securefs::full_format::FileTable::init(void):30]    Root directory not initialized, creating... (error 3 The system cannot find the path specified. (CreateFileW(path=\\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs\00\00000000000000000000000000000000000000000000000000000000000000.meta)))
[Info] [0000000000001AFC] [2025-08-01 16:13:43.616183300 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [0000000000001AFC] [2025-08-01 16:14:58.080707400 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [00000000000036E8] [2025-08-01 16:15:55.688610800 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [00000000000036E8] [2025-08-01 16:16:35.549534300 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000002D24] [2025-08-01 16:20:02.875506200 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [0000000000002D24] [2025-08-01 16:20:55.002023600 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000003964] [2025-08-01 16:22:33.073738200 UTC] [void __cdecl securefs::full_format::FileTable::init(void):30]    Root directory not initialized, creating... (error 3 The system cannot find the path specified. (CreateFileW(path=\\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs\00\00000000000000000000000000000000000000000000000000000000000000.meta)))
[Info] [0000000000003964] [2025-08-01 16:22:33.077533000 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Info] [0000000000003964] [2025-08-01 16:32:56.768180000 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000000B2C] [2025-08-02 10:25:49.235313200 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Warning] [0000000000000D3C] [2025-08-02 10:25:50.304152600 UTC] [bool __cdecl securefs::lite_format::`anonymous-namespace'::DirectoryImpl::next(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > *,struct fuse_stat *):695]    Skipping filename \\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs/00 due to exception in decoding: Cannot decode string with base32
[Info] [0000000000000B2C] [2025-08-02 10:26:16.832806400 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000002D6C] [2025-08-02 10:26:40.369853800 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Warning] [0000000000003008] [2025-08-02 10:26:41.377345400 UTC] [bool __cdecl securefs::lite_format::`anonymous-namespace'::DirectoryImpl::next(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > *,struct fuse_stat *):695]    Skipping filename \\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs/00 due to exception in decoding: Cannot decode string with base32
[Warning] [0000000000003008] [2025-08-02 10:26:41.377747400 UTC] [bool __cdecl securefs::lite_format::`anonymous-namespace'::DirectoryImpl::next(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > *,struct fuse_stat *):695]    Skipping filename \\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs/00 due to exception in decoding: Cannot decode string with base32
[Info] [0000000000002D6C] [2025-08-02 10:27:19.068447700 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
[Info] [0000000000000CE0] [2025-08-02 10:28:13.152361200 UTC] [void *__cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_28e73e012b2a475dc42aec207c37eec3>::operator ()(struct fuse_conn_info *) const:424]    Fuse operations initialized
[Warning] [0000000000001010] [2025-08-02 10:28:14.156273300 UTC] [bool __cdecl securefs::lite_format::`anonymous-namespace'::DirectoryImpl::next(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > *,struct fuse_stat *):695]    Skipping filename \\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs/00 due to exception in decoding: Cannot decode string with base32
[Warning] [000000000000070C] [2025-08-02 10:28:14.156616700 UTC] [bool __cdecl securefs::lite_format::`anonymous-namespace'::DirectoryImpl::next(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > *,struct fuse_stat *):695]    Skipping filename \\?\E:\Sync\Sanbroz\WORK\EncDec\securefs\fs/00 due to exception in decoding: Cannot decode string with base32
[Info] [0000000000000CE0] [2025-08-02 10:28:33.629388200 UTC] [auto __cdecl securefs::FuseHighLevelOpsBase::build_ops::<lambda_ffbc62efb7a09271d91a65fc29633a71>::operator ()(void *) const:428]    Fuse operations destroyed
