#SingleInstance Force
#Persistent

server_user=admin
server_pass=admin
server_ip=localhost
server_port=8090
connect_timeout=5
retry_count = 3

IniRead, saved_mnt, vfs.ini, Config, MountPath, c:\apps
IniRead, server_user, vfs.ini, Config, server_user, %server_user%
IniRead, server_pass, vfs.ini, Config, server_pass, %server_pass%
IniRead, server_ip, vfs.ini, Config, server_ip, %server_ip%
IniRead, server_port, vfs.ini, Config, server_port, %server_port%
IniRead, connect_timeout, vfs.ini, Config, connect_timeout, %connect_timeout%
IniRead, retry_count, vfs.ini, Config, retry_count, %retry_count%

RunWait, %comspec% /c bin\curl.exe --retry %retry_count% --connect-timeout %connect_timeout% --user "%server_user%:%server_pass%" "%server_ip%:%server_port%/auth" | clip,,hide
RetVal = %clipboard%

if (RetVal = Ok)
{
	if FileExist("vfs.exe") 
	{
		RunWait, vfs.exe Z4Q02Oeb70ib, , Hide UseErrorLevel
		RetError = %ErrorLevel%
		If RetError
		{
			FileAppend, %A_Now%: VFS Error %RetError%`n, vfs_mon.log
		}	
	}
}
Else
{
	if FileExist("vfs.exe") 
		RunWait, vfs.exe, , Hide UseErrorLevel
}